import re
import json
import traceback
from openai import AsyncOpenAI
from config import OpenaiConfig as config
from pydantic import BaseModel
from fastmcp import Client
from ai_mcp.server import mcp
from loguru import logger

def get_think_and_answer(content: str) -> tuple[str, str]:
    think_pattern = r'<think>(.*?)</think>'
    think_match = re.search(think_pattern, content, re.DOTALL)

    if think_match:
        # 提取reasoning_content
        reasoning_content = think_match.group(1).strip()
        # 从内容中删除<think></think>块
        answer = re.sub(think_pattern, '', content, flags=re.DOTALL).strip()
    else:
        reasoning_content = ''
        answer = content.strip()

    return reasoning_content, answer


class LLMResponse(BaseModel):
    success: bool
    reasoning_content: str
    answer: str
    prompt_tokens: int
    completion_tokens: int
    all_tokens: int
    tool_calls_used: bool = False
    tool_results: list = []



# 定义可用的工具
AVAILABLE_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "search_products",
            "description": "通过关键词搜索产品信息。当用户询问产品、商品或想要查找特定物品时使用此工具。",
            "parameters": {
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "搜索关键词，例如产品名称或品牌"
                    }
                },
                "required": ["name"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_product_info_by_id",
            "description": "根据产品ID获取产品的详细信息。当用户询问特定产品的详细信息时使用。",
            "parameters": {
                "type": "object",
                "properties": {
                    "product_id": {
                        "type": "string",
                        "description": "产品的唯一标识符ID"
                    }
                },
                "required": ["product_id"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_product_stock_by_id",
            "description": "根据产品ID查询产品的库存信息。当用户询问产品是否有货或库存情况时使用。",
            "parameters": {
                "type": "object",
                "properties": {
                    "product_id": {
                        "type": "string",
                        "description": "产品的唯一标识符ID"
                    }
                },
                "required": ["product_id"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_product_price_by_id",
            "description": "根据产品ID查询产品的价格信息。当用户询问产品价格或费用时使用。",
            "parameters": {
                "type": "object",
                "properties": {
                    "product_id": {
                        "type": "string",
                        "description": "产品的唯一标识符ID"
                    }
                },
                "required": ["product_id"]
            }
        }
    }
]


async def call_mcp_tool(tool_name: str, arguments: dict) -> dict:
    """
    调用MCP工具

    Args:
        tool_name: 工具名称
        arguments: 工具参数

    Returns:
        工具执行结果
    """
    try:
        logger.info(f"调用MCP工具: {tool_name}, 参数: {arguments}")
        async with Client(mcp) as client:
            result = await client.call_tool(tool_name, arguments)
            logger.info(f"MCP工具调用结果: {result}")

            # 处理MCP返回的TextContent对象
            if isinstance(result, list) and len(result) > 0:
                # 获取第一个TextContent对象的文本内容
                text_content = result[0]
                if hasattr(text_content, 'text'):
                    try:
                        # 尝试解析JSON
                        parsed_result = json.loads(text_content.text)
                        return parsed_result
                    except json.JSONDecodeError:
                        # 如果不是JSON，直接返回文本
                        return {"status": "success", "data": text_content.text}
                else:
                    return {"status": "success", "data": str(text_content)}
            else:
                return {"status": "success", "data": result}

    except Exception as e:
        logger.error(f"MCP工具调用失败: {str(e)}")
        return {"status": "error", "message": f"工具调用失败: {str(e)}"}


def get_client() -> AsyncOpenAI:
    """
    获取异步OpenAI客户端实例。
    该函数从配置文件中读取API密钥和基础URL，并返回一个AsyncOpenAI客户端实例。
    """
    client = AsyncOpenAI(
        api_key=config.OPENAI_API_KEY,  # 从配置文件读取API密钥
        base_url=config.OPENAI_BASE_URL  # 从配置文件读取基础URL
    )
    return client


def parse_tool_calls_from_text(text: str) -> list:
    """
    从文本中解析工具调用请求
    格式: <tool_call>function_name(arg1="value1", arg2="value2")</tool_call>
    """
    import re
    tool_calls = []

    # 匹配工具调用模式
    pattern = r'<tool_call>(\w+)\((.*?)\)</tool_call>'
    matches = re.findall(pattern, text, re.DOTALL)

    for function_name, args_str in matches:
        try:
            # 解析参数
            args = {}
            if args_str.strip():
                # 简单的参数解析 (支持 key="value" 格式)
                arg_pattern = r'(\w+)="([^"]*)"'
                arg_matches = re.findall(arg_pattern, args_str)
                for key, value in arg_matches:
                    args[key] = value

            tool_calls.append({
                "function_name": function_name,
                "arguments": args
            })
        except Exception as e:
            logger.error(f"解析工具调用参数失败: {e}")

    return tool_calls


async def get_llm_response(messages: list) -> LLMResponse:
    """
    使用指定的模型和消息列表获取LLM响应，支持基于文本的工具调用。
    """
    try:
        # 获取异步OpenAI客户端
        client = get_client()

        # 添加系统提示，告诉AI如何使用工具
        enhanced_messages = messages.copy()

        # 检查是否已有系统消息
        has_system_message = any(msg.get("role") == "system" for msg in enhanced_messages)

        if not has_system_message:
            system_prompt = """你是一个智能客服助手，可以帮助用户查询产品信息。你有以下工具可以使用：

1. search_products(name="关键词") - 搜索产品信息
2. get_product_info_by_id(product_id="产品ID") - 获取产品详细信息
3. get_product_stock_by_id(product_id="产品ID") - 查询产品库存
4. get_product_price_by_id(product_id="产品ID") - 查询产品价格

当用户询问产品相关问题时，请使用以下格式调用工具：
<tool_call>function_name(arg1="value1", arg2="value2")</tool_call>

例如：
- 搜索产品：<tool_call>search_products(name="星巴克")</tool_call>
- 查询详情：<tool_call>get_product_info_by_id(product_id="123456")</tool_call>

请保持礼貌和专业。"""

            enhanced_messages.insert(0, {"role": "system", "content": system_prompt})

        # 第一次调用LLM
        response = await client.chat.completions.create(
            model=config.OPENAI_MODEL,
            messages=enhanced_messages,
            temperature=config.TEMPERATURE,
            max_tokens=config.MAX_TOKENS,
            stream=False
        )

        total_prompt_tokens = response.usage.prompt_tokens
        total_completion_tokens = response.usage.completion_tokens
        total_tokens = response.usage.total_tokens
        tool_results = []
        tool_calls_used = False

        if response and response.choices:
            content = response.choices[0].message.content.strip()

            # 检查是否包含工具调用
            tool_calls = parse_tool_calls_from_text(content)

            if tool_calls:
                tool_calls_used = True
                logger.info(f"检测到 {len(tool_calls)} 个工具调用请求")

                # 执行工具调用
                tool_results_text = []
                for tool_call in tool_calls:
                    function_name = tool_call["function_name"]
                    function_args = tool_call["arguments"]

                    logger.info(f"执行工具调用: {function_name} with args: {function_args}")

                    # 调用MCP工具
                    tool_result = await call_mcp_tool(function_name, function_args)
                    tool_results.append({
                        "tool": function_name,
                        "args": function_args,
                        "result": tool_result
                    })

                    # 格式化工具结果
                    result_text = f"工具 {function_name} 的执行结果：\n{json.dumps(tool_result, ensure_ascii=False, indent=2)}"
                    tool_results_text.append(result_text)

                # 添加工具结果到对话历史
                enhanced_messages.append({
                    "role": "assistant",
                    "content": content
                })
                enhanced_messages.append({
                    "role": "user",
                    "content": f"工具执行结果：\n\n{chr(10).join(tool_results_text)}\n\n请基于以上工具执行结果，为用户提供完整的回答。"
                })

                # 再次调用LLM生成最终回答
                logger.info("基于工具结果生成最终回答")
                final_response = await client.chat.completions.create(
                    model=config.OPENAI_MODEL,
                    messages=enhanced_messages,
                    temperature=config.TEMPERATURE,
                    max_tokens=config.MAX_TOKENS,
                    stream=False
                )

                # 累加token使用量
                total_prompt_tokens += final_response.usage.prompt_tokens
                total_completion_tokens += final_response.usage.completion_tokens
                total_tokens += final_response.usage.total_tokens

                final_content = final_response.choices[0].message.content.strip()
            else:
                # 没有工具调用，直接使用原始回答
                final_content = content

            if final_content:
                reasoning_content, answer = get_think_and_answer(final_content)

                result = LLMResponse(
                    success=True,
                    reasoning_content=reasoning_content,
                    answer=answer,
                    prompt_tokens=total_prompt_tokens,
                    completion_tokens=total_completion_tokens,
                    all_tokens=total_tokens,
                    tool_calls_used=tool_calls_used,
                    tool_results=tool_results
                )
            else:
                result = LLMResponse(
                    success=False,
                    reasoning_content="",
                    answer="No response from the model.",
                    prompt_tokens=total_prompt_tokens,
                    completion_tokens=total_completion_tokens,
                    all_tokens=total_tokens,
                    tool_calls_used=tool_calls_used,
                    tool_results=tool_results
                )
        else:
            result = LLMResponse(
                success=False,
                reasoning_content="",
                answer="No response from the model.",
                prompt_tokens=0,
                completion_tokens=0,
                all_tokens=0,
                tool_calls_used=False,
                tool_results=[]
            )

    except Exception as e:
        traceback.print_exc()
        logger.error(f"LLM响应生成错误: {str(e)}")
        result = LLMResponse(
            success=False,
            reasoning_content="",
            answer=f"Error: {str(e)}",
            prompt_tokens=0,
            completion_tokens=0,
            all_tokens=0,
            tool_calls_used=False,
            tool_results=[]
        )

    return result