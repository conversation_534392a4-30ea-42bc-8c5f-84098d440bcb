<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能客服系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 0rem;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 1000px;
            margin: 0 auto;
            width: 100%;
            padding: 1rem;
            box-sizing: border-box;
        }
        .messages-container {
            flex: 1;
            overflow-y: auto;
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .message {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 8px;
            max-width: 80%;
        }
        .user-message {
            background-color: #3498db;
            color: white;
            align-self: flex-end;
            margin-left: auto;
        }
        .ai-message {
            background-color: #ecf0f1;
            color: #333;
            align-self: flex-start;
        }
        .input-container {
            display: flex;
            gap: 0.5rem;
        }
        #user-input {
            flex: 1;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            resize: none;
        }
        button {
            background-color: #2c3e50;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.8rem 1.5rem;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #34495e;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .system-message {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            margin: 0.5rem 0;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #3498db;
            animation: spin 1s ease-in-out infinite;
            vertical-align: middle;
            margin-left: 10px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .token-info {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        pre {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="header">
        <h3>智能客服系统</h3>
    </div>

    <div class="chat-container">
        <div class="messages-container" id="messages-container">
            <div class="message ai-message">
                您好，我是智能助手。请问有什么可以帮助您的？
            </div>
        </div>

        <div class="token-container" style="display: flex; gap: 0.5rem; margin-bottom: 0.5rem;">
            <input type="password" id="token-input" placeholder="输入API令牌..." style="flex: 1; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
            <button id="save-token-button" style="background-color: #2c3e50; color: white; border: none; border-radius: 4px; padding: 0.5rem; cursor: pointer; font-size: 14px;">保存</button>
        </div>

        <div class="input-container">
            <textarea id="user-input" placeholder="请输入您的问题..." rows="2"></textarea>
            <button id="send-button">发送</button>
        </div>
    </div>

    <script>
        // DOM元素
        const messagesContainer = document.getElementById('messages-container');
        const userInput = document.getElementById('user-input');
        const sendButton = document.getElementById('send-button');
        const tokenInput = document.getElementById('token-input');
        const saveTokenButton = document.getElementById('save-token-button');
        
        // 初始化变量
        const messages = [];
        const userId = 'user_' + Math.random().toString(36).substring(2, 10);
        const chatId = 'chat_' + Math.random().toString(36).substring(2, 10);
        let isWaitingForResponse = false;
        let apiToken = localStorage.getItem('apiToken') || '';
        
        // 如果存在保存的令牌，则填充到输入框中
        if (apiToken) {
            tokenInput.value = apiToken;
        }
        
        // 添加初始系统消息
        messages.push({
            role: "system",
            content: "你是一个智能客服助手，可以回答用户的各种问题。请保持礼貌和专业。"
        });
        
        // 事件监听
        sendButton.addEventListener('click', sendMessage);
        userInput.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        });
        
        // 保存令牌按钮事件
        saveTokenButton.addEventListener('click', () => {
            const token = tokenInput.value.trim();
            if (token) {
                apiToken = token;
                localStorage.setItem('apiToken', token);
                displaySystemMessage("令牌已保存");
            }
        });
        
        // 发送消息
        async function sendMessage() {
            const userMessage = userInput.value.trim();
            
            if (!userMessage || isWaitingForResponse) {
                return;
            }
            
            // 获取当前输入的token
            const currentToken = tokenInput.value.trim();
            if (!currentToken) {
                displaySystemMessage("请先输入API令牌");
                return;
            }
            
            // 显示用户消息
            displayMessage(userMessage, 'user');
            
            // 清空输入框
            userInput.value = '';
            
            // 添加到消息历史
            messages.push({
                role: "user",
                content: userMessage
            });
            
            // 显示加载状态
            isWaitingForResponse = true;
            displayLoadingIndicator();
            sendButton.disabled = true;
            
            try {
                // 生成消息ID
                const msgId = 'msg_' + Math.random().toString(36).substring(2, 10);
                
                // 准备请求体
                const requestBody = {
                    user_id: userId,
                    chat_id: chatId,
                    msg_id: msgId,
                    messages: messages
                };
                
                // 调用API
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const data = await response.json();
                
                // 移除加载指示器
                removeLoadingIndicator();
                
                // 处理响应
                if (data.code === 200 && data.data && data.data.success) {
                    // 显示AI回复
                    const aiMessage = data.data.answer || "抱歉，我无法回答这个问题。";
                    displayMessage(aiMessage, 'ai', data.data);
                    
                    // 添加到消息历史
                    messages.push({
                        role: "assistant",
                        content: aiMessage
                    });
                } else {
                    // 显示错误信息
                    const errorMessage = data.data && data.data.answer ? 
                        data.data.answer : "抱歉，出现了一些问题，请稍后再试。";
                    displaySystemMessage(`错误: ${errorMessage}`);
                }
            } catch (error) {
                console.error('Error:', error);
                removeLoadingIndicator();
                displaySystemMessage("连接错误，请检查网络或服务器状态。");
            } finally {
                isWaitingForResponse = false;
                sendButton.disabled = false;
                scrollToBottom();
            }
        }
        
        // 显示消息
        function displayMessage(text, sender, tokenInfo = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            // 处理代码块
            const formattedText = formatMessage(text);
            messageDiv.innerHTML = formattedText;
            
            // 如果有token信息，添加到消息中
            if (tokenInfo && (tokenInfo.prompt_tokens || tokenInfo.completion_tokens)) {
                const tokenInfoDiv = document.createElement('div');
                tokenInfoDiv.className = 'token-info';
                tokenInfoDiv.textContent = `输入: ${tokenInfo.prompt_tokens} | 输出: ${tokenInfo.completion_tokens} | 总计: ${tokenInfo.all_tokens} tokens`;
                messageDiv.appendChild(tokenInfoDiv);
            }
            
            messagesContainer.appendChild(messageDiv);
            scrollToBottom();
        }
        
        // 显示系统消息
        function displaySystemMessage(text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'system-message';
            messageDiv.textContent = text;
            messagesContainer.appendChild(messageDiv);
            scrollToBottom();
        }
        
        // 显示加载指示器
        function displayLoadingIndicator() {
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'system-message';
            loadingDiv.id = 'loading-indicator';
            loadingDiv.innerHTML = '正在思考 <span class="loading"></span>';
            messagesContainer.appendChild(loadingDiv);
            scrollToBottom();
        }
        
        // 移除加载指示器
        function removeLoadingIndicator() {
            const loadingDiv = document.getElementById('loading-indicator');
            if (loadingDiv) {
                loadingDiv.remove();
            }
        }
        
        // 格式化消息内容（处理换行和代码块）
        function formatMessage(text) {
            // 处理代码块 ```language code ```
            let formattedText = text.replace(/```([a-z]*)\n([\s\S]*?)\n```/g, '<pre><code>$2</code></pre>');
            
            // 处理普通换行
            formattedText = formattedText.replace(/\n/g, '<br>');
            
            return formattedText;
        }
        
        // 滚动到底部
        function scrollToBottom() {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // 如果没有保存的令牌，聚焦到令牌输入框，否则聚焦到消息输入框
        if (!apiToken) {
            tokenInput.focus();
        } else {
            userInput.focus();
        }
    </script>
</body>
</html>