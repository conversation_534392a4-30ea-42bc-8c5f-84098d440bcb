import os
from dotenv import load_dotenv


load_dotenv()


class MRPConfig:
    """配置类，读取环境变量中的配置信息"""

    # API基础URL
    API_BASE = os.getenv('MRP_API_BASE')

    # 应用密钥
    APP_SECRET = os.getenv('MRP_APP_SECRET')

    # API路径
    MRP_PRODUCT_SEARCH = os.getenv('MRP_PRODUCT_SEARCH')
    API_PRODUCT_INFO = os.getenv('MRP_PRODUCT_INFO')
    API_PRODUCT_PRICE = os.getenv('MRP_PRODUCT_PRICE')
    API_PRODUCT_STOCK = os.getenv('MRP_PRODUCT_STOCK')

    @classmethod
    def get_full_url(cls, api_path):
        """获取完整的API URL

        Args:
            api_path: API路径

        Returns:
            完整的API URL
        """
        return f"{cls.API_BASE}{api_path}"

    @classmethod
    def get_search_url(cls):
        """获取模糊查询API的完整URL"""
        return cls.get_full_url(cls.MRP_PRODUCT_SEARCH)

    @classmethod
    def get_product_info_url(cls):
        """获取精确查询产品信息API的完整URL"""
        return cls.get_full_url(cls.API_PRODUCT_INFO)

    @classmethod
    def get_product_price_url(cls):
        """获取精确查询产品价格API的完整URL"""
        return cls.get_full_url(cls.API_PRODUCT_PRICE)

    @classmethod
    def get_product_stock_url(cls):
        """获取精确查询产品库存API的完整URL"""
        return cls.get_full_url(cls.API_PRODUCT_STOCK)



class OpenaiConfig:
    # API 配置
    OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
    OPENAI_BASE_URL = os.environ.get("OPENAI_BASE_URL")
    OPENAI_MODEL = os.environ.get("OPENAI_MODEL")
    MAX_TOKENS = int(os.environ.get("OPENAI_MAX_TOKENS", "40960"))
    TEMPERATURE = float(os.environ.get("OPENAI_TEMPERATURE", "0.7"))


class APIConfig:
    # 服务器配置
    HOST = os.environ.get("API_HOST", "0.0.0.0")
    PORT = int(os.environ.get("API_PORT", 8000))
    API_TOKEN = os.environ.get("API_TOKEN", "1234567890")