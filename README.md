# 智能客服系统

智能客服是一个基于自然语言处理和机器学习的机器人，使用大模型作为后端，与客户进行智能问答。

## 接口说明

api 地址：/api/chat，POST方法，兼容OpenAI格式
请求参数：
    user_id: str = ""
    chat_id: str = ""
    msg_id: str = ""
    prompt: str = ""
    messages: Optional[List[ChatMessage]]


返回格式：
    code: int = 200
    message: str = "success"
    data: 
        user_id: str = ""
        chat_id: str = ""
        msg_id: str = ""
        answer_id: str = ""
        success: bool
        reasoning_content: str
        answer: str
        prompt_tokens: int
        completion_tokens: int
        all_tokens: int