from fastmcp import FastMCP
from typing import Optional, Dict, Any
import sys
import os

# 添加项目根目录到系统路径，确保可以导入 utils 模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ai_mcp.query import (
    get_products,
    get_info_by_id,
    get_stock_by_id,
    get_price_by_id
)

# 创建 FastMCP 应用实例
mcp = FastMCP(
    name="Product Query MCP Server",
    description="用于查询产品信息、库存和价格的MCP服务器",
    version="1.0.0",
)


@mcp.tool
def search_products(name: str) -> Dict[str, Any]:
    """
    通过关键词进行模糊查询产品信息

    Args:
        name: 搜索关键词

    Returns:
        包含产品搜索结果的字典
    """
    try:
        result = get_products(name)
        if "status" in result and result["status"] == "error":
            return {"status": "error", "message": result.get("message", "查询失败")}
        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": f"查询产品信息时发生错误: {str(e)}"}


@mcp.tool
def get_product_info_by_id(product_id: str) -> Dict[str, Any]:
    """
    根据产品ID查询产品详细信息

    Args:
        product_id: 产品ID

    Returns:
        包含产品详细信息的字典
    """
    try:
        result = get_info_by_id(product_id)
        if "status" in result and result["status"] == "error":
            return {"status": "error", "message": result.get("message", "查询失败")}
        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": f"查询产品信息时发生错误: {str(e)}"}


@mcp.tool
def get_product_stock_by_id(product_id: str) -> Dict[str, Any]:
    """
    根据产品ID查询产品的库存信息

    Args:
        product_id: 产品ID

    Returns:
        包含产品库存信息的字典
    """
    try:
        result = get_stock_by_id(product_id)
        if "status" in result and result["status"] == "error":
            return {"status": "error", "message": result.get("message", "查询失败")}
        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": f"查询库存信息时发生错误: {str(e)}"}


@mcp.tool
def get_product_price_by_id(product_id: str) -> Dict[str, Any]:
    """
    根据产品ID查询产品的价格信息

    Args:
        product_id: 产品ID

    Returns:
        包含产品价格信息的字典
    """
    try:
        result = get_price_by_id(product_id)
        if "status" in result and result["status"] == "error":
            return {"status": "error", "message": result.get("message", "查询失败")}
        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": f"查询价格信息时发生错误: {str(e)}"}

