#!/usr/bin/env python3
"""
测试MCP集成到智能问答服务的功能
"""

import asyncio
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.llm import get_llm_response
from loguru import logger


async def test_mcp_integration():
    """测试MCP集成功能"""
    print("=== 测试MCP集成到智能问答服务 ===\n")
    
    # 测试用例1: 产品搜索
    print("1. 测试产品搜索功能")
    messages1 = [
        {"role": "user", "content": "我想找一些星巴克的产品"}
    ]
    
    try:
        response1 = await get_llm_response(messages1)
        print(f"成功: {response1.success}")
        print(f"使用工具: {response1.tool_calls_used}")
        print(f"工具结果: {response1.tool_results}")
        print(f"回答: {response1.answer}")
        print(f"Token使用: {response1.all_tokens}")
        print("-" * 50)
    except Exception as e:
        print(f"测试1失败: {str(e)}")
        print("-" * 50)
    
    # 测试用例2: 产品详情查询
    print("\n2. 测试产品详情查询")
    messages2 = [
        {"role": "user", "content": "请帮我查询产品ID为0c131617-5d21-41c9-920e-859e18cbce98的详细信息"}
    ]
    
    try:
        response2 = await get_llm_response(messages2)
        print(f"成功: {response2.success}")
        print(f"使用工具: {response2.tool_calls_used}")
        print(f"工具结果: {response2.tool_results}")
        print(f"回答: {response2.answer}")
        print(f"Token使用: {response2.all_tokens}")
        print("-" * 50)
    except Exception as e:
        print(f"测试2失败: {str(e)}")
        print("-" * 50)
    
    # 测试用例3: 普通对话（不需要工具）
    print("\n3. 测试普通对话")
    messages3 = [
        {"role": "user", "content": "你好，请介绍一下你自己"}
    ]
    
    try:
        response3 = await get_llm_response(messages3)
        print(f"成功: {response3.success}")
        print(f"使用工具: {response3.tool_calls_used}")
        print(f"回答: {response3.answer}")
        print(f"Token使用: {response3.all_tokens}")
        print("-" * 50)
    except Exception as e:
        print(f"测试3失败: {str(e)}")
        print("-" * 50)
    
    # 测试用例4: 多轮对话
    print("\n4. 测试多轮对话")
    messages4 = [
        {"role": "system", "content": "你是一个智能客服助手"},
        {"role": "user", "content": "我想找一些咖啡产品"},
        {"role": "assistant", "content": "好的，我来帮您搜索咖啡产品。"},
        {"role": "user", "content": "请搜索星巴克相关的产品"}
    ]
    
    try:
        response4 = await get_llm_response(messages4)
        print(f"成功: {response4.success}")
        print(f"使用工具: {response4.tool_calls_used}")
        print(f"工具结果数量: {len(response4.tool_results)}")
        print(f"回答: {response4.answer}")
        print(f"Token使用: {response4.all_tokens}")
        print("-" * 50)
    except Exception as e:
        print(f"测试4失败: {str(e)}")
        print("-" * 50)


if __name__ == "__main__":
    asyncio.run(test_mcp_integration())
